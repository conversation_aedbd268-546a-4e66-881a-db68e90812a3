#!/usr/bin/env python3
"""
WISE波段统计分析主脚本

对HII区源表中的所有源进行WISE W3(12μm)和W4(22μm)波段的统计分析。
包括区域定义、背景噪声估算、统计量计算和可视化。

使用方法:
    python wise_band_statistics.py [--max-sources N] [--test-mode]

参数:
    --max-sources N: 限制处理的源数量（用于测试）
    --test-mode: 测试模式，只处理前5个源
    --output-dir: 输出目录，默认为'data/output'
    --config: 配置文件路径，默认为'config/default_config.yaml'
"""

import os
import sys
import argparse
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.wise_statistics import analyze_all_sources, create_statistics_visualization
from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description='WISE波段统计分析',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument('--max-sources', type=int, default=None,
                       help='限制处理的源数量（用于测试）')
    parser.add_argument('--test-mode', action='store_true',
                       help='测试模式，只处理前5个源')
    parser.add_argument('--output-dir', type=str, default='data/output',
                       help='输出目录')
    parser.add_argument('--config', type=str, default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--catalog', type=str, default=None,
                       help='源表路径（如果不指定则从配置文件读取）')

    args = parser.parse_args()

    # 设置日志记录器
    logger = setup_logger(name='wise_band_statistics')

    # 记录开始时间
    start_time = time.time()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    logger.info("="*80)
    logger.info("WISE波段统计分析开始")
    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("="*80)

    try:
        # 处理参数
        if args.test_mode:
            max_sources = 5
            logger.info("测试模式：只处理前5个源")
        else:
            max_sources = args.max_sources

        if max_sources is not None:
            logger.info(f"限制处理源数: {max_sources}")

        # 确保输出目录存在
        ensure_directory(args.output_dir)

        # 创建时间戳子目录
        timestamped_output_dir = os.path.join(args.output_dir, f'wise_statistics_{timestamp}')
        ensure_directory(timestamped_output_dir)

        logger.info(f"输出目录: {timestamped_output_dir}")
        logger.info(f"配置文件: {args.config}")

        # 步骤1: 批量分析所有源
        logger.info("步骤1: 开始批量分析所有源的WISE波段统计")
        results_df = analyze_all_sources(
            catalog_path=args.catalog,
            config_path=args.config,
            output_dir=timestamped_output_dir,
            max_sources=max_sources
        )

        logger.info(f"批量分析完成，共处理{len(results_df)}个源")

        # 统计成功和失败的数量
        import pandas as pd

        # 检查是否有error列
        if 'error' in results_df.columns:
            error_mask = results_df['error'].notna()
            successful_results = results_df[~error_mask]
            failed_results = results_df[error_mask]
        else:
            # 如果没有error列，说明所有结果都成功
            successful_results = results_df
            failed_results = pd.DataFrame()

        logger.info(f"成功分析: {len(successful_results)}个源")
        logger.info(f"分析失败: {len(failed_results)}个源")

        if len(failed_results) > 0:
            logger.warning("失败的源:")
            for _, row in failed_results.iterrows():
                error_msg = row.get('error', 'Unknown error')
                logger.warning(f"  {row['source_name']}: {error_msg}")

        # 步骤2: 创建可视化图表
        if len(successful_results) > 0:
            logger.info("步骤2: 创建统计结果可视化图表")
            create_statistics_visualization(results_df, timestamped_output_dir)
            logger.info("可视化图表创建完成")
        else:
            logger.warning("没有成功的分析结果，跳过可视化步骤")

        # 步骤3: 生成汇总报告
        logger.info("步骤3: 生成汇总报告")
        generate_summary_report(results_df, timestamped_output_dir, args)

        # 记录完成时间
        end_time = time.time()
        elapsed_time = end_time - start_time

        logger.info("="*80)
        logger.info("WISE波段统计分析完成")
        logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"总耗时: {elapsed_time/60:.2f}分钟")
        logger.info(f"结果保存在: {timestamped_output_dir}")
        logger.info("="*80)

        # 打印主要结果文件
        main_results_file = os.path.join(timestamped_output_dir, 'wise_band_statistics.csv')
        if os.path.exists(main_results_file):
            print(f"\n主要结果文件: {main_results_file}")

        visualization_files = [
            'wise_intensity_distributions.png',
            'w3_vs_w4_correlation.png',
            'background_distributions.png'
        ]

        print("\n可视化图表:")
        for viz_file in visualization_files:
            viz_path = os.path.join(timestamped_output_dir, viz_file)
            if os.path.exists(viz_path):
                print(f"  {viz_path}")

        summary_report = os.path.join(timestamped_output_dir, 'analysis_summary.txt')
        if os.path.exists(summary_report):
            print(f"\n汇总报告: {summary_report}")

    except Exception as e:
        logger.error(f"WISE波段统计分析失败: {str(e)}")
        logger.error("详细错误信息:", exc_info=True)
        sys.exit(1)

def generate_summary_report(results_df, output_dir, args):
    """
    生成汇总报告

    Args:
        results_df: 统计结果DataFrame
        output_dir: 输出目录
        args: 命令行参数
    """
    logger = setup_logger(name='wise_band_statistics')

    try:
        import pandas as pd
        import numpy as np

        # 过滤有效结果
        if 'error' in results_df.columns:
            error_mask = results_df['error'].notna()
            valid_results = results_df[~error_mask]
            failed_results = results_df[error_mask]
        else:
            valid_results = results_df
            failed_results = pd.DataFrame()

        # 生成报告内容
        report_lines = []
        report_lines.append("WISE波段统计分析汇总报告")
        report_lines.append("="*60)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 处理统计
        report_lines.append("处理统计:")
        report_lines.append(f"  总源数: {len(results_df)}")
        report_lines.append(f"  成功分析: {len(valid_results)}")
        report_lines.append(f"  分析失败: {len(failed_results)}")
        report_lines.append(f"  成功率: {len(valid_results)/len(results_df)*100:.1f}%")
        report_lines.append("")

        if len(valid_results) > 0:
            # 背景统计
            report_lines.append("背景水平统计:")
            if 'w3_background' in valid_results.columns:
                w3_bg_stats = valid_results['w3_background'].describe()
                report_lines.append(f"  W3背景: 均值={w3_bg_stats['mean']:.6f}, 标准差={w3_bg_stats['std']:.6f}")
                report_lines.append(f"          范围=[{w3_bg_stats['min']:.6f}, {w3_bg_stats['max']:.6f}]")

            if 'w4_background' in valid_results.columns:
                w4_bg_stats = valid_results['w4_background'].describe()
                report_lines.append(f"  W4背景: 均值={w4_bg_stats['mean']:.6f}, 标准差={w4_bg_stats['std']:.6f}")
                report_lines.append(f"          范围=[{w4_bg_stats['min']:.6f}, {w4_bg_stats['max']:.6f}]")
            report_lines.append("")

            # 各区域强度统计
            regions = ['1R', '3R', '5R']
            bands = ['W3', 'W4']

            report_lines.append("区域强度统计 (背景扣除后):")
            for region in regions:
                report_lines.append(f"  {region}区域:")
                for band in bands:
                    mean_col = f'{region}_{band}_mean'
                    rms_col = f'{region}_{band}_rms'

                    if mean_col in valid_results.columns:
                        mean_stats = valid_results[mean_col].describe()
                        report_lines.append(f"    {band}均值: {mean_stats['mean']:.6f} ± {mean_stats['std']:.6f}")

                    if rms_col in valid_results.columns:
                        rms_stats = valid_results[rms_col].describe()
                        report_lines.append(f"    {band}RMS:  {rms_stats['mean']:.6f} ± {rms_stats['std']:.6f}")
                report_lines.append("")

        # 失败源列表
        if len(failed_results) > 0:
            report_lines.append("分析失败的源:")
            for _, row in failed_results.iterrows():
                error_msg = row.get('error', 'Unknown error')
                # 截断过长的错误信息
                if len(error_msg) > 100:
                    error_msg = error_msg[:97] + "..."
                report_lines.append(f"  {row['source_name']}: {error_msg}")
            report_lines.append("")

        # 输出文件信息
        report_lines.append("输出文件:")
        report_lines.append(f"  主要结果: wise_band_statistics.csv")
        report_lines.append(f"  可视化图表:")
        report_lines.append(f"    - wise_intensity_distributions.png")
        report_lines.append(f"    - w3_vs_w4_correlation.png")
        report_lines.append(f"    - background_distributions.png")
        report_lines.append("")

        # 参数信息
        report_lines.append("运行参数:")
        report_lines.append(f"  配置文件: {args.config}")
        report_lines.append(f"  输出目录: {args.output_dir}")
        if args.max_sources:
            report_lines.append(f"  限制源数: {args.max_sources}")
        if args.test_mode:
            report_lines.append(f"  测试模式: 是")

        # 保存报告
        report_file = os.path.join(output_dir, 'analysis_summary.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        logger.info(f"汇总报告已保存到: {report_file}")

    except Exception as e:
        logger.error(f"生成汇总报告失败: {str(e)}")

if __name__ == "__main__":
    main()
